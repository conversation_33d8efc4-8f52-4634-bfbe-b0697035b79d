'use client';

import React, { useState } from 'react';
import { <PERSON>, Gem, Sparkles, Droplet, Star, DollarSign, RefreshCw } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { CurrencyCard, DiamondPackageCard, CurrencyItem, DiamondPackage } from './CurrencyCard';

interface CurrencySectionProps {
  lang: string;
  featured?: boolean;
}

const CurrencySection: React.FC<CurrencySectionProps> = ({ lang, featured = false }) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeTab, setActiveTab] = useState<'purchase' | 'exchange'>('purchase');

  // Primary currency (purchased with USD)
  const endora = {
    id: 'endora',
    name: 'Star Endora',
    description: 'Premium currency purchased with real money',
    icon: Star,
    color: 'from-yellow-400 to-orange-500',
    balance: 2500,
    purchaseRate: '1 USD = 100 Endora'
  };

  // Secondary currencies (exchanged with endora)
  const currencies: CurrencyItem[] = [
    {
      id: 'alphane',
      name: '<PERSON><PERSON>',
      description: 'Used for character interactions and special abilities',
      uses: 'Character conversations, special actions',
      icon: Flame,
      color: 'from-orange-400 to-red-500',
      balance: 1250,
      exchangeRate: '1 Endora = 10 Alphane',
      diamondCost: 0.1
    },
    {
      id: 'serotile',
      name: 'Serotile',
      description: 'Special tokens for memory creation and customization',
      uses: 'Memory arts, scene creation, custom content',
      icon: Sparkles,
      color: 'from-purple-400 to-pink-500',
      balance: 23,
      exchangeRate: '5 Endora = 1 Serotile',
      diamondCost: 5
    },
    {
      id: 'oxytol',
      name: 'Oxytol',
      description: 'Relationship currency for deepening character connections',
      uses: 'Relationship progression, intimate scenes, special bonds',
      icon: Droplet,
      color: 'from-green-400 to-teal-500',
      balance: 89,
      exchangeRate: '2 Endora = 1 Oxytol',
      diamondCost: 2
    },
  ];

  const endoraPackages: DiamondPackage[] = [
    {
      name: 'Starter Bundle',
      price: 4.99,
      amount: 500,
      bonus: 100,
      discount: 15,
    },
    {
      name: 'Popular Choice',
      price: 9.99,
      amount: 1200,
      bonus: 300,
      popular: true,
      discount: 25,
    },
    {
      name: 'Best Value Pack',
      price: 19.99,
      amount: 2800,
      bonus: 700,
      bestValue: true,
      discount: 30,
    },
    {
      name: 'Premium Elite',
      price: 49.99,
      amount: 7500,
      bonus: 2500,
      discount: 35,
    },
    {
      name: 'Ultimate Treasure',
      price: 99.99,
      amount: 16000,
      bonus: 6000,
      limited: true,
      discount: 40,
    },
    {
      name: 'Legendary Vault',
      price: 199.99,
      amount: 35000,
      bonus: 15000,
      limited: true,
      discount: 45,
    }
  ];

  const displayCurrencies = featured ? currencies.slice(0, 2) : currencies;
  const displayPackages = featured ? endoraPackages.slice(0, 2) : endoraPackages;

  const handleCurrencyExchange = (currency: CurrencyItem, amount: number) => {
    console.log(`Exchanging ${amount} endora for ${currency.name}`);
    // TODO: Implement exchange logic
  };

  const handleEndoraPurchase = (pkg: DiamondPackage) => {
    console.log(`Purchasing ${pkg.name} for $${pkg.price}`);
    // TODO: Implement purchase logic
  };

  return (
    <div className="space-y-6">

      {/* Tab Navigation */}
      {!featured && (
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('purchase')}
            className={`flex-1 py-2 px-4 rounded-md font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
              activeTab === 'purchase'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            <DollarSign className="w-4 h-4" />
            {t('store.currency.purchaseEndora')}
          </button>
          <button
            onClick={() => setActiveTab('exchange')}
            className={`flex-1 py-2 px-4 rounded-md font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
              activeTab === 'exchange'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            <RefreshCw className="w-4 h-4" />
            {t('store.currency.exchangeCurrencies')}
          </button>
        </div>
      )}

      {/* Current Balance */}
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4 shadow-lg shadow-yellow-500/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
              <Star className="w-6 h-6 text-white" fill="currentColor" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {t('store.currency.yourBalance')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {endora.purchaseRate}
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-yellow-700 dark:text-yellow-300">
              {endora.balance.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              {t('store.currency.starEndora')}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {(activeTab === 'purchase' || featured) && (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {t('store.currency.purchaseStarEndora')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('store.currency.purchaseDescription')}
              </p>
            </div>
          </div>

          {/* Unified Responsive Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {displayPackages.map((pkg, index) => (
              <DiamondPackageCard
                key={index}
                package={pkg}
                onPurchase={handleEndoraPurchase}
                isLoading={false}
                lang={lang}
              />
            ))}
          </div>
        </div>
      )}

      {/* Exchange Tab Content */}
      {activeTab === 'exchange' && !featured && (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
              <RefreshCw className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {t('store.currency.exchangeTitle')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('store.currency.exchangeDescription')}
              </p>
            </div>
          </div>

          {/* Unified Responsive Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {currencies.map((currency) => (
              <CurrencyCard
                key={currency.id}
                currency={currency}
                onExchange={handleCurrencyExchange}
                isLoading={false}
                variant="exchange"
                lang={lang}
              />
            ))}
          </div>
        </div>
      )}

      {/* Currency Information for Featured Mode */}
      {featured && (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
              <RefreshCw className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {t('store.currency.availableCurrencies')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('store.currency.exchangeForSpecialized')}
              </p>
            </div>
          </div>

          {/* Unified Responsive Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {displayCurrencies.map((currency) => (
              <CurrencyCard
                key={currency.id}
                currency={currency}
                variant="display"
                lang={lang}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrencySection; 