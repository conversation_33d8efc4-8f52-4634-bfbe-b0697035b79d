'use client';

import React, { useState } from 'react';
import { Calendar, Heart, Star, Clock, ArrowRight, Check, Loader2, Users, Trophy, Sparkles } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

export interface MemorialItem {
  id: string;
  characterName: string;
  characterAvatar: string;
  memorialType: 'first_meeting' | 'perfect_intimacy' | 'perfect_storyline';
  anniversaryDate: Date;
  daysCount: number;
  title: string;
  description: string;
  rewards: {
    type: 'exclusive_content' | 'special_scene' | 'bonus_currency';
    name: string;
    value: string;
  }[];
  timeRemaining: number; // hours remaining
  isClaimed?: boolean;
  isExpired?: boolean;
}

interface MemorialCardProps {
  item: MemorialItem;
  onClaim: (item: MemorialItem) => void;
  isLoading?: boolean;
  variant?: 'mobile' | 'desktop' | 'responsive';
  lang: string;
}

const MemorialCard: React.FC<MemorialCardProps> = ({
  item,
  onClaim,
  isLoading = false,
  variant = 'responsive',
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const canClaim = !item.isClaimed && !item.isExpired && item.timeRemaining > 0;

  const handleClaimClick = () => {
    if (canClaim) {
      setShowConfirmModal(true);
    }
  };

  const handleConfirmClaim = () => {
    setShowConfirmModal(false);
    onClaim(item);
  };

  // Responsive card classes based on variant
  const getCardClasses = () => {
    const baseClasses = "bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group relative overflow-hidden";

    if (variant === 'mobile') {
      // Legacy mobile horizontal scroll variant
      return `flex-shrink-0 w-64 xs:w-72 sm:w-80 p-4 sm:p-6 ${baseClasses}`;
    }

    if (variant === 'desktop') {
      // Legacy desktop variant
      return `w-full p-4 sm:p-6 ${baseClasses}`;
    }

    // New responsive variant - adapts to grid container
    return `w-full p-4 md:p-6 min-h-[400px] flex flex-col ${baseClasses}`;
  };

  const getMemorialTypeConfig = () => {
    switch (item.memorialType) {
      case 'first_meeting':
        return {
          icon: Users,
          label: t('store.memorial.types.first_meeting'),
          color: 'from-blue-400 to-cyan-500',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          textColor: 'text-blue-600 dark:text-blue-400'
        };
      case 'perfect_intimacy':
        return {
          icon: Heart,
          label: t('store.memorial.types.perfect_intimacy'),
          color: 'from-pink-400 to-rose-500',
          bgColor: 'bg-pink-50 dark:bg-pink-900/20',
          textColor: 'text-pink-600 dark:text-pink-400'
        };
      case 'perfect_storyline':
        return {
          icon: Trophy,
          label: t('store.memorial.types.perfect_storyline'),
          color: 'from-yellow-400 to-orange-500',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          textColor: 'text-yellow-600 dark:text-yellow-400'
        };
    }
  };

  const getTimeRemainingText = () => {
    if (item.timeRemaining <= 0) return t('store.memorial.timeRemaining.expired');
    if (item.timeRemaining < 24) return t('store.memorial.timeRemaining.hoursLeft', { hours: item.timeRemaining });
    const days = Math.floor(item.timeRemaining / 24);
    return t('store.memorial.timeRemaining.daysLeft', { days });
  };

  const getTimeRemainingColor = () => {
    if (item.timeRemaining <= 0) return 'text-gray-500';
    if (item.timeRemaining < 24) return 'text-red-500';
    if (item.timeRemaining < 72) return 'text-orange-500';
    return 'text-green-500';
  };

  const config = getMemorialTypeConfig();
  const IconComponent = config.icon;

  return (
    <>
      <div className={getCardClasses()}>
        {/* Background decoration */}
        <div className={`absolute top-0 right-0 w-24 h-24 bg-gradient-to-br ${config.color} opacity-10 rounded-full -translate-y-6 translate-x-6`} />

        {/* Header with memorial type and time */}
        <div className="flex items-start justify-between mb-3 sm:mb-4 relative z-0">
          <div className={`flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
            <IconComponent className="w-3 h-3" />
            {config.label}
          </div>
          <div className={`flex items-center gap-1 text-xs font-medium ${getTimeRemainingColor()}`}>
            <Clock className="w-3 h-3" />
            {getTimeRemainingText()}
          </div>
        </div>

        {/* Character and content - Flexible */}
        <div className="flex-1 flex flex-col relative z-0">
          <div className="flex items-center gap-3 mb-3">
            <img
              src={item.characterAvatar}
              alt={item.characterName}
              className="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-white dark:border-gray-700 shadow-md flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 line-clamp-2">
                {item.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('store.memorial.characterInfo', { characterName: item.characterName, daysCount: item.daysCount })}
              </p>
            </div>
          </div>
          
          <div className="flex-1">
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-4">
              {item.description}
            </p>

            {/* Rewards */}
            <div className="space-y-2">
              {item.rewards.slice(0, 2).map((reward, index) => (
                <div key={index} className="flex items-center gap-2 text-xs">
                  <Sparkles className="w-3 h-3 text-purple-500 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300 font-medium flex-1 truncate">{reward.name}</span>
                  <span className={`px-1.5 py-0.5 rounded text-xs font-bold ${config.bgColor} ${config.textColor} flex-shrink-0`}>
                    {reward.value}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Action button - Fixed at bottom */}
          <div className="mt-auto pt-4">
            <button
              onClick={handleClaimClick}
              disabled={!canClaim || isLoading}
              className={`w-full py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 relative z-0 ${
                canClaim && !isLoading
                  ? `bg-gradient-to-r ${config.color} text-white hover:shadow-lg hover:shadow-current/30 group-hover:scale-105`
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-3 h-3 animate-spin" />
                  {t('store.memorial.actions.processing')}
                </>
              ) : canClaim ? (
                <>
                  <Calendar className="w-3 h-3" />
                  {t('store.memorial.actions.claimMemorial')}
                  <ArrowRight className="w-3 h-3" />
                </>
              ) : (
                <>
                  <Check className="w-3 h-3" />
                  {item.isClaimed ? t('store.memorial.actions.claimed') : t('store.memorial.timeRemaining.expired')}
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
              Claim Memorial Rewards
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Claim your memorial rewards for {item.characterName}?
            </p>

            {/* Rewards preview */}
            <div className={`${config.bgColor} border border-current/20 rounded-lg p-4 mb-4`}>
              <div className="space-y-2">
                {item.rewards.map((reward, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {reward.name}
                    </span>
                    <span className={`text-sm font-bold ${config.textColor}`}>
                      {reward.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmClaim}
                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium text-white bg-gradient-to-r ${config.color} hover:shadow-lg transition-all`}
              >
                Claim Now
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MemorialCard;
