'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Gift, Clock, <PERSON>, Spark<PERSON>, Crown, PartyPopper, Cake, Trophy, Zap, Users, Target } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface MemorialDaySectionProps {
  lang: string;
}

interface PersonalCelebration {
  id: string;
  characterName: string;
  characterImage?: string;
  celebrationType: 'character_birthday' | 'friendship_anniversary' | 'affection_milestone' | 'first_meeting' | 'relationship_milestone' | 'special_achievement' | 'conversation_milestone';
  celebrationDate: Date;
  milestone?: {
    type: 'days_known' | 'affection_level' | 'conversations' | 'special_moments';
    value: number;
    description: string;
  };
  specialOffers: {
    type: 'discount' | 'exclusive_content' | 'bonus_currency' | 'special_unlock';
    name: string;
    description: string;
    value: string;
    originalPrice?: number;
    discountedPrice?: number;
    icon: React.ComponentType<any>;
    color: string;
  }[];
  timeRemaining: number; // hours remaining
  claimed: boolean;
  priority: number; // For sorting celebrations
}

const MemorialDaySection: React.FC<MemorialDaySectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every hour to keep countdown accurate
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 3600000); // Update every hour

    return () => clearInterval(interval);
  }, []);

  // Mock personal celebrations - in real app this would come from user's interaction history and AI analytics
  const personalCelebrations: PersonalCelebration[] = [
    // Character Birthday
    {
      id: '1',
      characterName: 'Luna Nightshade',
      celebrationType: 'character_birthday',
      celebrationDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      specialOffers: [
        {
          type: 'exclusive_content',
          name: 'Birthday Special Scene',
          description: 'Exclusive birthday celebration with Luna',
          value: 'FREE',
          icon: Cake,
          color: 'from-pink-400 to-rose-500'
        },
        {
          type: 'discount',
          name: 'Birthday Discount',
          description: '70% off all Luna content',
          value: '70% OFF',
          originalPrice: 25,
          discountedPrice: 7.5,
          icon: Gift,
          color: 'from-purple-400 to-pink-500'
        }
      ],
      timeRemaining: 48, // 2 days left
      claimed: false,
      priority: 1
    },
    // Friendship Anniversary
    {
      id: '2',
      characterName: 'Alexander Sterling',
      celebrationType: 'friendship_anniversary',
      celebrationDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      milestone: {
        type: 'days_known',
        value: 30,
        description: '30 days of friendship'
      },
      specialOffers: [
        {
          type: 'discount',
          name: 'Friendship Milestone Discount',
          description: 'Special discount on all scenes with Alexander',
          value: '50% OFF',
          originalPrice: 20,
          discountedPrice: 10,
          icon: Heart,
          color: 'from-red-400 to-pink-500'
        },
        {
          type: 'exclusive_content',
          name: 'Friendship Memory',
          description: 'Exclusive anniversary scene and dialogue',
          value: 'FREE',
          icon: Sparkles,
          color: 'from-purple-400 to-pink-500'
        }
      ],
      timeRemaining: 72, // 3 days left
      claimed: false,
      priority: 2
    },
    // Affection Level Achievement
    {
      id: '3',
      characterName: 'Sophia Chen',
      celebrationType: 'affection_milestone',
      celebrationDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      milestone: {
        type: 'affection_level',
        value: 75,
        description: 'Reached 75% affection level'
      },
      specialOffers: [
        {
          type: 'special_unlock',
          name: 'Intimate Conversations',
          description: 'Unlock deeper conversation topics',
          value: 'UNLOCKED',
          icon: Heart,
          color: 'from-red-400 to-pink-500'
        },
        {
          type: 'bonus_currency',
          name: 'Affection Bonus',
          description: 'Special currency bonus for your achievement',
          value: '750 Endora',
          icon: Star,
          color: 'from-blue-400 to-indigo-500'
        }
      ],
      timeRemaining: 120, // 5 days left
      claimed: false,
      priority: 3
    },
    // Conversation Milestone
    {
      id: '4',
      characterName: 'Marcus Thompson',
      celebrationType: 'conversation_milestone',
      celebrationDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      milestone: {
        type: 'conversations',
        value: 100,
        description: '100 conversations completed'
      },
      specialOffers: [
        {
          type: 'special_unlock',
          name: 'Advanced Dialogue Options',
          description: 'Unlock complex conversation paths',
          value: 'UNLOCKED',
          icon: Users,
          color: 'from-green-400 to-emerald-500'
        },
        {
          type: 'discount',
          name: 'Conversation Master Discount',
          description: '40% off all conversation packs',
          value: '40% OFF',
          originalPrice: 30,
          discountedPrice: 18,
          icon: Zap,
          color: 'from-blue-400 to-indigo-500'
        }
      ],
      timeRemaining: 96, // 4 days left
      claimed: false,
      priority: 4
    },
    // Special Achievement
    {
      id: '5',
      characterName: 'Isabella Rodriguez',
      celebrationType: 'special_achievement',
      celebrationDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      milestone: {
        type: 'special_moments',
        value: 10,
        description: 'Created 10 special memories together'
      },
      specialOffers: [
        {
          type: 'exclusive_content',
          name: 'Memory Master Collection',
          description: 'Exclusive memory scenes and artwork',
          value: 'EXCLUSIVE',
          icon: Trophy,
          color: 'from-yellow-400 to-orange-500'
        },
        {
          type: 'bonus_currency',
          name: 'Achievement Reward',
          description: 'Special achievement bonus',
          value: '1000 Memory Puzzles',
          icon: Star,
          color: 'from-purple-400 to-pink-500'
        }
      ],
      timeRemaining: 48, // 2 days left
      claimed: false,
      priority: 5
    }
  ];

  const getCelebrationIcon = (type: string) => {
    switch (type) {
      case 'character_birthday': return Cake;
      case 'friendship_anniversary': return Heart;
      case 'affection_milestone': return Target;
      case 'first_meeting': return PartyPopper;
      case 'relationship_milestone': return Crown;
      case 'special_achievement': return Trophy;
      case 'conversation_milestone': return Users;
      default: return Calendar;
    }
  };

  const getCelebrationTitle = (celebration: PersonalCelebration) => {
    switch (celebration.celebrationType) {
      case 'character_birthday': return `${celebration.characterName}'s Birthday!`;
      case 'friendship_anniversary': return `${celebration.milestone?.value} Days of Friendship`;
      case 'affection_milestone': return `${celebration.milestone?.value}% Affection Achieved!`;
      case 'first_meeting': return `First Meeting Anniversary`;
      case 'relationship_milestone': return `Relationship Milestone`;
      case 'special_achievement': return `Special Achievement Unlocked!`;
      case 'conversation_milestone': return `${celebration.milestone?.value} Conversations!`;
      default: return 'Personal Celebration';
    }
  };

  const getCelebrationDescription = (celebration: PersonalCelebration) => {
    switch (celebration.celebrationType) {
      case 'character_birthday': return `Celebrate ${celebration.characterName}'s special day with exclusive content`;
      case 'friendship_anniversary': return `Commemorating your ${celebration.milestone?.value} days of friendship with ${celebration.characterName}`;
      case 'affection_milestone': return `You've reached ${celebration.milestone?.value}% affection with ${celebration.characterName}!`;
      case 'conversation_milestone': return `You've had ${celebration.milestone?.value} meaningful conversations with ${celebration.characterName}`;
      case 'special_achievement': return `You've created ${celebration.milestone?.value} special memories with ${celebration.characterName}`;
      default: return celebration.milestone?.description || 'A special moment to celebrate';
    }
  };

  const getTimeRemainingText = (hours: number) => {
    if (hours >= 24) {
      const days = Math.floor(hours / 24);
      return `${days} day${days > 1 ? 's' : ''} left`;
    }
    return `${hours} hour${hours > 1 ? 's' : ''} left`;
  };

  const getUrgencyColor = (hours: number) => {
    if (hours <= 24) return 'text-red-600 dark:text-red-400';
    if (hours <= 72) return 'text-orange-600 dark:text-orange-400';
    return 'text-green-600 dark:text-green-400';
  };

  const handleClaimOffer = (celebrationId: string, offerIndex: number) => {
    // Handle claim logic here
    console.log(`Claiming offer ${offerIndex} for celebration ${celebrationId}`);
  };

  // Sort celebrations by priority and filter active ones
  const activeCelebrations = personalCelebrations
    .filter(celebration => !celebration.claimed && celebration.timeRemaining > 0)
    .sort((a, b) => a.priority - b.priority);

  if (activeCelebrations.length === 0) {
    return null; // Don't show section if no active celebrations
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-8 h-8 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg flex items-center justify-center">
            <Cake className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Personal Celebrations
          </h2>
          <div className="flex items-center gap-1 bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 px-2 py-1 rounded-full text-sm font-medium">
            <Clock className="w-3 h-3" />
            Limited Time
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          Birthdays • Friendship milestones • Affection achievements • Special moments
        </p>
      </div>

      {/* Celebration Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {activeCelebrations.map((celebration) => {
          const CelebrationIcon = getCelebrationIcon(celebration.celebrationType);

          return (
            <div
              key={celebration.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200 relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-400/10 to-rose-500/10 rounded-full -translate-y-8 translate-x-8" />

              {/* Header */}
              <div className="flex items-start justify-between mb-4 relative z-10">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-500 rounded-xl flex items-center justify-center shadow-lg">
                    <CelebrationIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      {getCelebrationTitle(celebration)}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {celebration.characterName}
                    </p>
                  </div>
                </div>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(celebration.timeRemaining)} bg-current/10`}>
                  <Clock className="w-3 h-3" />
                  {getTimeRemainingText(celebration.timeRemaining)}
                </div>
              </div>

              {/* Celebration Info */}
              <div className="mb-6 relative z-10">
                <div className="flex items-center gap-2 mb-3">
                  <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Date: {celebration.celebrationDate.toLocaleDateString()}
                  </span>
                </div>
                <div className="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 border border-pink-200 dark:border-pink-800 rounded-lg p-3">
                  <p className="text-sm text-pink-800 dark:text-pink-200 font-medium">
                    🎉 {getCelebrationDescription(celebration)}
                  </p>
                </div>
              </div>

              {/* Special Offers */}
              <div className="space-y-3 relative z-10">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Celebration Rewards:
                </h4>
                {celebration.specialOffers.map((offer, index) => {
                  const OfferIcon = offer.icon;
                  
                  return (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 bg-gradient-to-br ${offer.color} rounded-lg flex items-center justify-center`}>
                          <OfferIcon className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                            {offer.name}
                          </h5>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {offer.description}
                          </p>
                          {offer.originalPrice && offer.discountedPrice && (
                            <div className="flex items-center gap-2 mt-1">
                              <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
                                ${offer.discountedPrice}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400 line-through">
                                ${offer.originalPrice}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r ${offer.color}`}>
                          {offer.value}
                        </span>
                        <button
                          onClick={() => handleClaimOffer(celebration.id, index)}
                          className="bg-gradient-to-r from-pink-500 to-rose-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:shadow-lg hover:shadow-pink-500/30 transition-all duration-200"
                        >
                          Claim
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Progress indicator */}
              <div className="mt-4 relative z-10">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                  <span>Offer expires in:</span>
                  <span>{getTimeRemainingText(celebration.timeRemaining)}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      celebration.timeRemaining <= 24 ? 'bg-red-500' :
                      celebration.timeRemaining <= 72 ? 'bg-orange-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.max(10, (celebration.timeRemaining / 168) * 100)}%` }}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Info Box */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Heart className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-1">
              About Personal Celebrations
            </h4>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              Personal Celebrations honor your unique journey with each character. From birthdays and friendship milestones to
              affection achievements and conversation records, each celebration offers exclusive rewards that reflect your special bond.
              These limited-time offers celebrate the depth of your relationships!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemorialDaySection;
