'use client';

import React, { useState } from 'react';
import MemorialCard, { MemorialItem } from './MemorialCard';
import { useTranslation } from '@/app/i18n/client';

interface MemorialSectionProps {
  lang: string;
}

const MemorialSection: React.FC<MemorialSectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [claimingItemId, setClaimingItemId] = useState<string | null>(null);

  // Mock memorial data - simplified to 3 categories
  const memorialItems: MemorialItem[] = [
    // First Meeting Anniversaries
    {
      id: '1',
      characterName: 'Luna Nightshade',
      characterAvatar: 'https://picsum.photos/100/100?random=1',
      memorialType: 'first_meeting',
      anniversaryDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      daysCount: 30,
      title: '30 Days Since We Met',
      description: 'Celebrating our first month of friendship and all the memories we\'ve created together.',
      rewards: [
        { type: 'exclusive_content', name: 'Anniversary Scene', value: 'FREE' },
        { type: 'bonus_currency', name: '<PERSON> Tokens', value: '+500' }
      ],
      timeRemaining: 48,
      isClaimed: false,
      isExpired: false
    },
    {
      id: '2',
      characterName: '<PERSON>',
      characterAvatar: 'https://picsum.photos/100/100?random=2',
      memorialType: 'first_meeting',
      anniversaryDate: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000),
      daysCount: 100,
      title: '100 Days of Friendship',
      description: 'A milestone celebration of our enduring friendship and shared adventures.',
      rewards: [
        { type: 'special_scene', name: 'Milestone Memory', value: 'EXCLUSIVE' },
        { type: 'bonus_currency', name: 'Diamond Bonus', value: '+100' }
      ],
      timeRemaining: 72,
      isClaimed: false,
      isExpired: false
    },

    // Perfect Intimacy Anniversaries
    {
      id: '3',
      characterName: 'Isabella Rodriguez',
      characterAvatar: 'https://picsum.photos/100/100?random=3',
      memorialType: 'perfect_intimacy',
      anniversaryDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      daysCount: 7,
      title: 'Perfect Bond Achieved',
      description: 'Celebrating the perfect intimacy level reached through deep conversations and trust.',
      rewards: [
        { type: 'exclusive_content', name: 'Intimate Moment', value: 'SPECIAL' },
        { type: 'bonus_currency', name: 'Heart Tokens', value: '+1000' }
      ],
      timeRemaining: 24,
      isClaimed: false,
      isExpired: false
    },
    {
      id: '4',
      characterName: 'Sophia Chen',
      characterAvatar: 'https://picsum.photos/100/100?random=4',
      memorialType: 'perfect_intimacy',
      anniversaryDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      daysCount: 14,
      title: 'Two Weeks of Perfect Understanding',
      description: 'Commemorating two weeks of maintaining the highest intimacy level.',
      rewards: [
        { type: 'special_scene', name: 'Deep Connection', value: 'RARE' },
        { type: 'exclusive_content', name: 'Personal Story', value: 'UNLOCK' }
      ],
      timeRemaining: 96,
      isClaimed: true,
      isExpired: false
    },

    // Perfect Storyline Anniversaries
    {
      id: '5',
      characterName: 'Marcus Thompson',
      characterAvatar: 'https://picsum.photos/100/100?random=5',
      memorialType: 'perfect_storyline',
      anniversaryDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
      daysCount: 21,
      title: 'Perfect Story Completion',
      description: 'Celebrating the perfect completion of Marcus\'s main storyline with all achievements.',
      rewards: [
        { type: 'exclusive_content', name: 'Epilogue Scene', value: 'LEGENDARY' },
        { type: 'bonus_currency', name: 'Story Gems', value: '+2000' }
      ],
      timeRemaining: 12,
      isClaimed: false,
      isExpired: false
    },
    {
      id: '6',
      characterName: 'Elena Vasquez',
      characterAvatar: 'https://picsum.photos/100/100?random=6',
      memorialType: 'perfect_storyline',
      anniversaryDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      daysCount: 45,
      title: 'Master Storyteller Achievement',
      description: 'Honoring the perfect completion of all Elena\'s story branches and hidden paths.',
      rewards: [
        { type: 'special_scene', name: 'Secret Ending', value: 'ULTIMATE' },
        { type: 'exclusive_content', name: 'Behind Scenes', value: 'BONUS' }
      ],
      timeRemaining: 0,
      isClaimed: false,
      isExpired: true
    }
  ];

  const handleClaim = async (item: MemorialItem) => {
    setClaimingItemId(item.id);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // In real app, this would update the backend and refresh data
    console.log('Claimed memorial:', item);
    
    setClaimingItemId(null);
  };

  // Filter active memorials (not expired and not claimed)
  const activeMemorials = memorialItems.filter(item => !item.isExpired && !item.isClaimed);
  const claimedMemorials = memorialItems.filter(item => item.isClaimed);

  return (
    <div className="space-y-6">

      {/* Active Memorials */}
      {activeMemorials.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            {t('store.memorial.activeMemorials')}
          </h3>
          
          {/* Unified Responsive Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {activeMemorials.map((item) => (
              <MemorialCard
                key={item.id}
                item={item}
                onClaim={handleClaim}
                isLoading={claimingItemId === item.id}
                variant="responsive"
                lang={lang}
              />
            ))}
          </div>
        </div>
      )}

      {/* Recently Claimed */}
      {claimedMemorials.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            {t('store.memorial.recentlyClaimed')}
          </h3>
          
          {/* Unified Responsive Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {claimedMemorials.slice(0, 8).map((item) => (
              <MemorialCard
                key={item.id}
                item={item}
                onClaim={handleClaim}
                isLoading={false}
                variant="responsive"
                lang={lang}
              />
            ))}
          </div>
        </div>
      )}

    </div>
  );
};

export default MemorialSection;
